#!/usr/bin/env python3
"""
Comprehensive test script to verify that all model handlers support the new parameters.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bfcl._llm_response_generation import build_handler
from bfcl.constants.model_config import MODEL_CONFIG_MAPPING

def test_all_handlers():
    """Test that all model handlers support the new parameters."""
    
    # Test parameters
    test_temperature = 0.7
    test_top_k = 50
    test_top_p = 0.9
    
    print("Testing all model handlers for parameter support...")
    print(f"Test parameters: temperature={test_temperature}, top_k={test_top_k}, top_p={test_top_p}")
    print()
    
    success_count = 0
    error_count = 0
    
    # Test a sample of different model types
    test_models = [
        "gpt-4o-mini-2024-07-18-FC",  # OpenAI
        "claude-3-5-sonnet-20241022-FC",  # <PERSON>
        "command-a-03-2025-FC",  # Cohere
        "nova-pro-v1",  # Amazon Nova
        "firefunction-v2-FC",  # Fireworks
        "NousResearch/Hermes-2-Pro-Llama-3-8B",  # Local Hermes
        "THUDM/glm-4-9b-chat",  # GLM
        "openbmb/MiniCPM3-4B-FC",  # MiniCPM
        "Salesforce/xLAM-1b-fc-r",  # Salesforce Llama
        "Salesforce/xLAM-7b-fc-r",  # Salesforce Qwen
    ]
    
    for model_name in test_models:
        if model_name not in MODEL_CONFIG_MAPPING:
            print(f"⚠️  Model {model_name} not found in MODEL_CONFIG_MAPPING")
            continue
            
        try:
            handler = build_handler(model_name, test_temperature, test_top_k, test_top_p)
            
            # Check if parameters are correctly set
            assert handler.temperature == test_temperature, f"Temperature mismatch: {handler.temperature} != {test_temperature}"
            assert handler.top_k == test_top_k, f"Top-k mismatch: {handler.top_k} != {test_top_k}"
            assert handler.top_p == test_top_p, f"Top-p mismatch: {handler.top_p} != {test_top_p}"
            
            print(f"✅ {model_name}: Parameters correctly set")
            success_count += 1
            
        except Exception as e:
            print(f"❌ {model_name}: Error - {e}")
            error_count += 1
    
    print()
    print(f"Summary: {success_count} successful, {error_count} errors")
    
    if error_count == 0:
        print("🎉 All tested handlers support the new parameters!")
    else:
        print(f"⚠️  {error_count} handlers need attention")

def test_backwards_compatibility():
    """Test that old code still works (backwards compatibility)."""
    
    print("\nTesting backwards compatibility...")
    
    model_name = "gpt-4o-mini-2024-07-18-FC"
    if model_name in MODEL_CONFIG_MAPPING:
        try:
            # Test old-style call (only temperature)
            handler = build_handler(model_name, 0.5)
            
            assert handler.temperature == 0.5
            assert handler.top_k == -1  # Default
            assert handler.top_p == 1.0  # Default
            
            print("✅ Backwards compatibility test passed")
            
        except Exception as e:
            print(f"❌ Backwards compatibility test failed: {e}")

if __name__ == "__main__":
    test_all_handlers()
    test_backwards_compatibility()
    print("\nTesting completed!")
